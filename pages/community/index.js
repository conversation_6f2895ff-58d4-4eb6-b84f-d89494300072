const app = getApp();
const api = require('../../api/index');
const communityApi = require('../../api/community');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 帖子列表
    posts: [],
    // 当前选中的帖子ID（用于评论）
    currentPostId: null,
    // 评论列表
    comments: [],
    // 加载状态
    loading: false,
    // 评论加载状态
    commentLoading: false,
    // 是否已加载全部
    loadedAll: false,
    // 每页加载数量
    pageSize: 10,
    // 跳过的数量（分页）
    skip: 0,
    // 是否显示评论区
    showComments: false,
    // 定时器ID
    refreshTimer: null,
    // 评论总数
    commentTotal: 0,
    // 评论是否全部加载完成
    commentLoadedAll: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('社区页面加载');
    // 加载帖子列表
    this.loadPosts();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('社区页面显示，启动定时刷新');
    this.startAutoRefresh();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    console.log('社区页面隐藏，停止定时刷新');
    this.stopAutoRefresh();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    console.log('社区页面卸载，清理定时器');
    this.stopAutoRefresh();
  },

  /**
   * 启动自动刷新
   */
  startAutoRefresh: function () {
    // 清除之前的定时器
    this.stopAutoRefresh();
    
    // 设置30秒定时刷新
    const timer = setInterval(() => {
      console.log('定时刷新触发');
      this.silentRefresh();
    }, 30000); // 30秒
    
    this.setData({ refreshTimer: timer });
  },

  /**
   * 停止自动刷新
   */
  stopAutoRefresh: function () {
    if (this.data.refreshTimer) {
      clearInterval(this.data.refreshTimer);
      this.setData({ refreshTimer: null });
    }
  },

  /**
   * 静默刷新（不显示loading状态）
   */
  silentRefresh: function () {
    console.log('执行静默刷新');
    
    // 获取最新的帖子（只获取第一页）
    api.community.getPosts({
      skip: 0,
      limit: this.data.pageSize
    })
    .then(res => {
      const response = res || { items: [], total: 0 };
      const newPosts = response.items || [];
      
      console.log('静默刷新获取到帖子数量:', newPosts.length);
      
      if (newPosts.length > 0) {
        // 检查是否有新帖子
        const existingIds = this.data.posts.map(post => post.id);
        const reallyNewPosts = newPosts.filter(post => !existingIds.includes(post.id));
        
        if (reallyNewPosts.length > 0) {
          console.log('发现新帖子数量:', reallyNewPosts.length);
          // 将新帖子添加到列表顶部
          this.setData({
            posts: [...reallyNewPosts, ...this.data.posts]
          });
          
          // 可选：显示新帖子提示
          wx.showToast({
            title: `发现${reallyNewPosts.length}条新动态`,
            icon: 'none',
            duration: 2000
          });
        }
      }
    })
    .catch(err => {
      console.error('静默刷新失败:', err);
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    console.log('用户下拉刷新');
    // 重置分页并刷新
    this.setData({
      posts: [],
      skip: 0,
      loadedAll: false
    });
    this.loadPosts().then(() => {
      wx.stopPullDownRefresh();
      console.log('下拉刷新完成');
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    console.log('触底加载更多，当前状态:', {
      loadedAll: this.data.loadedAll,
      loading: this.data.loading,
      postsCount: this.data.posts.length
    });
    
    if (!this.data.loadedAll && !this.data.loading) {
      this.loadMorePosts();
    }
  },

  /**
   * 加载帖子列表
   */
  loadPosts: function () {
    if (this.data.loading) {
      console.log('正在加载中，跳过重复请求');
      return Promise.resolve();
    }

    console.log('开始加载帖子，skip:', this.data.skip, 'limit:', this.data.pageSize);
    this.setData({ loading: true });
    
    return api.community.getPosts({
      skip: this.data.skip,
      limit: this.data.pageSize
    })
    .then(res => {
      console.log('API响应原始数据:', res);
      
      // 修正数据解析逻辑 - API直接返回数据，不需要再取.data
      const response = res || { items: [], total: 0 };
      const newPosts = response.items || [];
      const total = response.total || 0;
      
      console.log('解析后的数据:', {
        newPostsCount: newPosts.length,
        total: total,
        currentPostsCount: this.data.posts.length
      });
      
      // 详细打印第一个帖子的数据结构
      if (newPosts.length > 0) {
        console.log('第一个帖子数据结构:', JSON.stringify(newPosts[0], null, 2));
      }
      
      const updatedPosts = this.data.posts.concat(newPosts);
      const isLoadedAll = newPosts.length < this.data.pageSize || updatedPosts.length >= total;
      
      this.setData({
        posts: updatedPosts,
        skip: this.data.skip + newPosts.length,
        loadedAll: isLoadedAll,
        loading: false
      });
      
      console.log('页面数据更新完成:', {
        totalPosts: updatedPosts.length,
        skip: this.data.skip + newPosts.length,
        loadedAll: isLoadedAll
      });
      
      // 添加调试：检查页面数据是否正确设置
      setTimeout(() => {
        console.log('延迟检查页面数据:', {
          postsInData: this.data.posts.length,
          firstPost: this.data.posts[0]
        });
      }, 100);
    })
    .catch(err => {
      console.error('加载帖子失败:', err);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    });
  },

  /**
   * 加载更多帖子
   */
  loadMorePosts: function () {
    console.log('加载更多帖子');
    this.loadPosts();
  },

  /**
   * 点赞帖子
   */
  onLikePost: function (e) {
    const { postId, isLiked } = e.detail;
    console.log('点赞操作:', { postId, isLiked });
    
    // 调用点赞或取消点赞API
    const apiCall = isLiked ? 
      api.community.likePost(postId) : 
      api.community.unlikePost(postId);
    
    apiCall.catch(err => {
      console.error('点赞操作失败:', err);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      
      // 回滚本地状态
      const posts = this.data.posts.map(post => {
        if (post.id === postId) {
          post.is_liked = !isLiked;
          post.like_count = isLiked ? (post.like_count - 1) : (post.like_count + 1);
        }
        return post;
      });
      
      this.setData({ posts });
    });
  },

  /**
   * 加载评论
   */
  onLoadComments: function (e) {
    const { postId } = e.detail;
    console.log('加载评论，postId:', postId);
    
    this.setData({
      currentPostId: postId,
      showComments: true,
      comments: [],
      commentLoading: true
    });
    
    api.community.getPostComments(postId)
      .then(res => {
        console.log('评论API响应:', res);
        
        // 修正数据解析逻辑 - API直接返回数据
        const response = res || { items: [], total: 0 };
        const comments = response.items || [];
        const total = response.total || 0;
        
        console.log('解析后的评论数据:', { commentsCount: comments.length, total });
        
        this.setData({
          comments: comments,
          commentTotal: total,
          commentLoading: false,
          commentLoadedAll: comments.length < 20 || comments.length >= total
        });
      })
      .catch(err => {
        console.error('加载评论失败:', err);
        wx.showToast({
          title: '加载评论失败',
          icon: 'none'
        });
        this.setData({ commentLoading: false });
      });
  },

  /**
   * 提交评论
   */
  onSubmitComment: function (e) {
    const { postId, commentData } = e.detail;
    console.log('提交评论:', { postId, commentData });
    
    api.community.createComment(postId, commentData)
      .then(res => {
        console.log('评论提交响应:', res);
        
        // 修正响应处理逻辑 - API直接返回评论数据
        const newComment = res?.comment || res;
        if (!newComment) {
          console.error('评论创建成功但返回数据结构异常');
          return;
        }
        
        // 添加新评论到列表顶部
        this.setData({
          comments: [newComment, ...this.data.comments]
        });
        
        // 更新帖子评论计数
        const posts = this.data.posts.map(post => {
          if (post.id === postId) {
            post.comment_count = (post.comment_count || 0) + 1;
          }
          return post;
        });
        
        this.setData({ posts });
        
        wx.showToast({
          title: '评论成功',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('提交评论失败:', err);
        wx.showToast({
          title: '评论失败，请重试',
          icon: 'none'
        });
      });
  },

  /**
   * 点赞评论
   */
  onLikeComment: function (e) {
    const { commentId, isLiked } = e.detail;
    console.log('评论点赞操作:', { commentId, isLiked });
    
    // 调用点赞或取消点赞API
    const apiCall = isLiked ? 
      api.community.likeComment(commentId) : 
      api.community.unlikeComment(commentId);
    
    apiCall.catch(err => {
      console.error('评论点赞操作失败:', err);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      
      // 此处应回滚本地状态，但为简化代码暂不实现
    });
  },

  /**
   * 加载更多评论
   */
  onLoadMoreComments: function (e) {
    const { postId, skip } = e.detail;
    console.log('加载更多评论:', { postId, skip });
    
    this.setData({ commentLoading: true });
    
    api.community.getPostComments(postId, { skip, limit: 20 })
      .then(res => {
        console.log('更多评论API响应:', res);
        
        // 修正数据解析逻辑 - API直接返回数据
        const response = res || { items: [], total: 0 };
        const newComments = response.items || [];
        const total = response.total || 0;
        
        console.log('解析后的更多评论数据:', { newCommentsCount: newComments.length, total });
        
        this.setData({
          comments: [...this.data.comments, ...newComments],
          commentLoading: false,
          commentLoadedAll: newComments.length < 20 || this.data.comments.length + newComments.length >= total
        });
      })
      .catch(err => {
        console.error('加载更多评论失败:', err);
        this.setData({ commentLoading: false });
      });
  },

  /**
   * 点击用户头像
   */
  onTapAvatar: function (e) {
    const { userId } = e.detail;
    wx.navigateTo({
      url: `/pages/user-profile/index?userId=${userId}`
    });
  },

  /**
   * 点击帖子
   */
  onTapPost: function (e) {
    const { postId } = e.detail;
    wx.navigateTo({
      url: `/pages/post-detail/index?postId=${postId}`
    });
  },

  /**
   * 点击训练内容
   */
  onTapWorkout: function (e) {
    const { workoutId, postId } = e.detail;
    wx.navigateTo({
      url: `/pages/post-detail/index?postId=${postId}&workoutId=${workoutId}`
    });
  },

  /**
   * 分享帖子
   */
  onSharePost: function (e) {
    const { postId } = e.detail;
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 编辑帖子
   */
  onEditPost: function (e) {
    const { postId } = e.detail;
    wx.navigateTo({
      url: `/pages/post-edit/index?postId=${postId}`
    });
  },

  /**
   * 删除帖子
   */
  onDeletePost: function (e) {
    const { postId } = e.detail;
    
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除吗？',
      success: (res) => {
        if (res.confirm) {
          // TODO: 实现删除帖子的API调用
          // 从本地列表中移除帖子
          const posts = this.data.posts.filter(post => post.id !== postId);
          this.setData({ posts });
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 举报帖子
   */
  onReportPost: function (e) {
    const { postId } = e.detail;
    
    wx.showModal({
      title: '举报',
      content: '确定要举报这条内容吗？',
      success: (res) => {
        if (res.confirm) {
          api.community.reportPost(postId, { reason: '内容违规' })
            .then(() => {
              wx.showToast({
                title: '举报成功',
                icon: 'success'
              });
            })
            .catch(err => {
              console.error('举报失败:', err);
              wx.showToast({
                title: '举报失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  /**
   * 跳转到发布页面
   */
  navigateToPublish: function () {
    wx.navigateTo({
      url: '/pages/post-publish/index'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: 'ScienceFit社区',
      path: '/pages/community/index'
    };
  },

  /**
   * 处理关注事件
   */
  onFollowUser(e) {
    const { userId, isFollowing } = e.detail;
    console.log('关注用户:', userId, '操作:', isFollowing ? '关注' : '取消关注');
    
    this.followUser(userId, isFollowing);
  },

  /**
   * 关注/取消关注用户
   */
  async followUser(userId, isFollowing) {
    try {
      wx.showLoading({ title: isFollowing ? '关注中...' : '取消关注中...' });
      
      const result = await communityApi.followUser(userId, isFollowing);
      
      if (result.success) {
        // 更新帖子列表中的关注状态
        const posts = this.data.posts.map(post => {
          if (post.user && post.user.id === userId) {
            return {
              ...post,
              user: {
                ...post.user,
                is_following: isFollowing,
                follower_count: result.data.follower_count || post.user.follower_count
              }
            };
          }
          return post;
        });
        
        this.setData({ posts });
        
        wx.showToast({
          title: isFollowing ? '关注成功' : '取消关注成功',
          icon: 'success'
        });
      } else {
        throw new Error(result.message || '操作失败');
      }
    } catch (error) {
      console.error('关注操作失败:', error);
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },
}); 