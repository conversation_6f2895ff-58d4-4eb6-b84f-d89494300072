<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <image src="/images/icons/search.png" class="search-icon" />
      <input class="search-input" placeholder="搜索动作" bindinput="onSearchInput" />
    </view>
  </view>

  <!-- 分类和侧边栏 -->
  <view class="content">
    <!-- 左侧肌肉部位栏 -->
    <view class="side-bar">
      <scroll-view scroll-y="true" class="muscle-scroll">
        <block wx:for="{{muscleGroups}}" wx:key="id">
          <view class="muscle-item {{item.selected ? 'selected' : ''}}" bindtap="selectMuscleGroup" data-id="{{item.id}}">
            {{item.name}}
          </view>
        </block>
      </scroll-view>
    </view>

    <!-- 右侧内容区 -->
    <view class="right-content">
      <!-- 分类标签（吸顶效果） -->
      <view class="sticky-category-bar">
        <scroll-view scroll-x="true" class="category-scroll" enhanced="true" show-scrollbar="false">
          <block wx:for="{{categories}}" wx:key="id">
            <view class="category-item {{item.selected ? 'selected' : ''}}" bindtap="selectCategory" data-id="{{item.id}}">
              {{item.name}}
            </view>
          </block>
        </scroll-view>
      </view>

      <!-- 动作列表 -->
      <scroll-view
        scroll-y="true"
        class="content-area {{hasBottomBarPadding ? 'has-bottom-bar' : ''}}"
        scroll-into-view="{{scrollToView}}"
        bindscrolltolower="onReachBottom"
        lower-threshold="50"
        enhanced="true"
        bounces="true"
        show-scrollbar="false">

        <!-- 加载中状态 -->
        <view class="loading-container" wx:if="{{isLoading}}">
          <view class="loading"></view>
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:elif="{{showEmptyState}}">
          <image class="empty-icon" src="/images/icons/no-data.png" mode="aspectFit" />
          <view class="empty-text">未找到相关动作</view>
          <view class="empty-subtext">请尝试其他分类或搜索条件</view>
        </view>

        <!-- 动作列表内容 -->
        <view wx:else class="exercises-container">
          <view wx:for="{{exerciseGroups}}" wx:key="index" wx:for-item="group" wx:for-index="groupIdx">
            <view class="exercise-section" id="category{{group.index}}">
              <view class="section-title">{{group.category}}</view>
              <view class="exercises-list">
                <block wx:for="{{group.exercises}}" wx:key="id">
                  <view class="exercise-card" data-index="{{index}}" data-id="{{item.id}}" bindtap="selectExercise">
                    <image
                      class="exercise-image {{item.full_gif_url ? 'gif-image' : ''}}"
                      src="{{item.full_gif_url || item.full_image_url}}"
                      mode="aspectFill"
                      lazy-load="true"
                      binderror="onImageError"
                      data-id="{{item.id}}"
                    />
                    <view class="exercise-info">
                      <view class="exercise-name">{{item.name}}</view>
                      <view class="exercise-meta">
                        <text class="exercise-part">{{item.move_cate}}</text>
                        <text class="exercise-level level-{{item.level}}">{{item.levelText}}</text>
                      </view>
                    </view>
                    <view class="selection-indicator {{selectedExercises[item.id] ? 'selected' : ''}}">
                      <text class="selection-icon">✓</text>
                    </view>
                  </view>
                </block>
              </view>
            </view>
          </view>

          <!-- 加载更多/无更多数据 -->
          <view class="loading-more" wx:if="{{isLoadingMore}}">
            <view class="loading"></view>
            <text>加载更多...</text>
          </view>
          <view class="no-more" wx:if="{{!hasMore && exercisesList.length > 0}}">
            没有更多数据了
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 使用原生按钮组件 - 最终版本 -->
  <view class="action-bar-wrapper" wx:if="{{Object.keys(selectedExercises).length > 0 || hasBottomBarPadding}}">
    <view class="action-bar">
      <button type="primary" plain="{{false}}" size="default" class="action-button" bindtap="confirmSelection">
        完成 ({{Object.keys(selectedExercises).length}})
      </button>
    </view>
  </view>


</view>
