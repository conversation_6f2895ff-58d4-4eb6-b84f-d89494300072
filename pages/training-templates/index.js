const { BASE_URL, API_BASE_URL } = require('../../api/config');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    templates: [],
    isLoading: true,
    isEmpty: false,
    selectedTemplateId: null,
    isShowDeleteConfirm: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.fetchTemplates();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.fetchTemplates().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 获取用户的训练模板列表
   */
  async fetchTemplates() {
    this.setData({ isLoading: true });
    
    try {
      // 使用API_BASE_URL，需要确保页面顶部引入配置
      const { API_BASE_URL } = require('../../api/config');
      
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: `${API_BASE_URL}/training-templates/`,
          method: 'GET',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('token')}`
          },
          success: (res) => resolve(res),
          fail: (err) => reject(err)
        });
      });
      
      console.log('获取模板结果:', res);
      
      if (res.statusCode === 200) {
        const templates = res.data || [];
        
        // 格式化创建时间
        templates.forEach(template => {
          if (template.created_at) {
            // API返回的created_at已经是ISO格式字符串
            const date = new Date(template.created_at);
            template.formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
          } else {
            template.formattedDate = '未知日期';
          }
        });
        
        this.setData({
          templates,
          isLoading: false,
          isEmpty: templates.length === 0
        });
      } else {
        throw new Error(res.data?.detail || '获取失败');
      }
    } catch (error) {
      console.error('获取训练模板失败:', error);
      this.setData({
        isLoading: false,
        isEmpty: true
      });
      wx.showToast({
        title: '获取模板失败',
        icon: 'none'
      });
    }
  },

  /**
   * 使用训练模板
   */
  useTemplate(e) {
    const { id } = e.currentTarget.dataset;
    const template = this.data.templates.find(t => t.id === id);
    
    if (!template) return;
    
    wx.showLoading({
      title: '加载模板...',
      mask: true
    });
    
    // 将模板数据添加到自定义训练计划
    wx.navigateTo({
      url: '/pages/customizePlan/index?fromTemplate=true',
      success: async (res) => {
        // 传递模板数据到自定义训练计划页面
        res.eventChannel.emit('acceptTemplateData', { template });
        wx.hideLoading();
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('跳转失败:', err);
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 显示删除确认框
   */
  showDeleteConfirm(e) {
    const { id } = e.currentTarget.dataset;
    this.setData({
      selectedTemplateId: id,
      isShowDeleteConfirm: true
    });
  },

  /**
   * 取消删除
   */
  cancelDelete() {
    this.setData({
      selectedTemplateId: null,
      isShowDeleteConfirm: false
    });
  },

  /**
   * 确认删除模板
   */
  async confirmDelete() {
    const { selectedTemplateId } = this.data;
    
    if (!selectedTemplateId) {
      this.setData({ isShowDeleteConfirm: false });
      return;
    }
    
    wx.showLoading({
      title: '删除中...',
      mask: true
    });
    
    try {
      // 使用API_BASE_URL
      const { API_BASE_URL } = require('../../api/config');
      
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: `${API_BASE_URL}/training-templates/${selectedTemplateId}`,
          method: 'DELETE',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('token')}`
          },
          success: (res) => resolve(res),
          fail: (err) => reject(err)
        });
      });
      
      wx.hideLoading();
      this.setData({ isShowDeleteConfirm: false });
      
      if (res.statusCode === 200) {
        // 从列表中移除已删除的模板
        const updatedTemplates = this.data.templates.filter(
          t => t.id !== selectedTemplateId
        );
        
        this.setData({
          templates: updatedTemplates,
          isEmpty: updatedTemplates.length === 0
        });
        
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
      } else {
        throw new Error(res.data?.detail || '删除失败');
      }
    } catch (error) {
      wx.hideLoading();
      this.setData({ isShowDeleteConfirm: false });
      console.error('删除训练模板失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack();
  }
}); 