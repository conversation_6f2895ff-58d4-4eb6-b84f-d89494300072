/* pages/training-templates/index.wxss */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}

/* 顶部标题栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.placeholder {
  width: 60rpx;
}

/* 加载中状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 150rpx 0;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 150rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-hint {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  padding: 0 60rpx;
}

/* 模板列表 */
.templates-list {
  padding: 20rpx 30rpx;
}

.template-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  background-color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.template-item:active {
  transform: scale(0.98);
}

.template-content {
  padding: 30rpx 30rpx 20rpx;
  box-sizing: border-box;
}

.template-info {
  width: 100%;
}

.template-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  word-wrap: break-word;
  word-break: break-all;
}

.template-stats {
  display: flex;
  flex-direction: column;
}

.template-count {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.template-date {
  font-size: 24rpx;
  color: #999;
}

/* 底部操作区域 */
.template-footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.use-template {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 60rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);
  transition: all 0.2s ease;
}

.use-template:active {
  background-color: #06b055;
  transform: scale(0.95);
}

.delete-template {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  border: 1rpx solid #e0e0e0;
  border-radius: 30rpx;
  color: #f44336;
  font-size: 26rpx;
  transition: all 0.2s ease;
}

.delete-template:active {
  background-color: #f5f5f5;
  transform: scale(0.95);
}

.delete-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 6rpx;
}

/* 确认弹窗 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  padding: 40rpx 30rpx 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.modal-message {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
}

.modal-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  margin: 0 10rpx;
  font-size: 30rpx;
}

.modal-button.cancel {
  background-color: #f5f5f5;
  color: #333;
}

.modal-button.confirm {
  background-color: #f44336;
  color: #fff;
  font-weight: 500;
} 