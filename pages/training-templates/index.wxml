<!--pages/training-templates/index.wxml-->
<view class="container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/icons/left arrow.png" class="back-icon"></image>
    </view>
    <view class="title">训练模板</view>
    <view class="placeholder"></view>
  </view>
  
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{isEmpty}}">
    <image class="empty-icon" src="/images/icons/body.png" mode="aspectFit"></image>
    <text class="empty-text">暂无训练模板</text>
    <text class="empty-hint">在自定义训练计划页面，可以保存当前训练为模板</text>
  </view>
  
  <!-- 模板列表 -->
  <view class="templates-list" wx:else>
    <block wx:for="{{templates}}" wx:key="id">
      <view class="template-item">
        <!-- 模板内容区域 -->
        <view class="template-content">
          <view class="template-info">
            <view class="template-name">{{item.template_name || '未命名模板'}}</view>
            <view class="template-stats">
              <text class="template-count">{{item.exercise_count || 0}}个训练动作</text>
              <text class="template-date">创建于 {{item.formattedDate}}</text>
            </view>
          </view>
        </view>
        
        <!-- 底部操作区域 -->
        <view class="template-footer">
          <view class="use-template" bindtap="useTemplate" data-id="{{item.id}}">使用</view>
          <view class="delete-template" catchtap="showDeleteConfirm" data-id="{{item.id}}">
            <image src="/images/icons/delete.png" class="delete-icon"></image>
            <text>删除</text>
          </view>
        </view>
      </view>
    </block>
  </view>
  
  <!-- 删除确认弹窗 -->
  <view class="modal-mask" wx:if="{{isShowDeleteConfirm}}">
    <view class="modal-content">
      <view class="modal-title">确认删除</view>
      <view class="modal-message">确定要删除这个训练模板吗？</view>
      <view class="modal-buttons">
        <button class="modal-button cancel" bindtap="cancelDelete">取消</button>
        <button class="modal-button confirm" bindtap="confirmDelete">删除</button>
      </view>
    </view>
  </view>
</view> 