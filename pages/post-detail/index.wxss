/* 帖子详情页面样式 */
.post-detail-container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部输入栏留出空间 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 帖子内容区域 */
.post-content-section {
  background: #fff;
  margin-bottom: 20rpx;
}

/* 用户信息 */
.post-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

.follow-btn {
  height: 60rpx;
  padding: 0 24rpx;
  background: #007AFF;
  color: #fff;
  border: none;
  border-radius: 30rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.follow-btn.following {
  background: #E5E5EA;
  color: #666;
}

.follow-btn::after {
  border: none;
}

/* 帖子文本内容 */
.post-text-content {
  padding: 24rpx;
}

.post-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.post-text {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
}

/* 图片展示 */
.post-images {
  margin: 0 24rpx 24rpx;
}

.image-swiper {
  height: 500rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.post-image {
  width: 100%;
  height: 100%;
}

/* 交互按钮 */
.post-actions {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-item {
  display: flex;
  align-items: center;
  margin-right: 60rpx;
  padding: 10rpx 0;
}

.action-item:last-child {
  margin-right: 0;
  margin-left: auto;
}

.action-icon {
  font-size: 40rpx;
  margin-right: 12rpx;
}

.action-text {
  font-size: 28rpx;
  color: #666;
}

.action-item.liked .action-text {
  color: #FF3B30;
}

/* 训练详情章节 */
.workout-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.toggle-icon {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

/* 训练统计 */
.workout-stats {
  display: flex;
  padding: 24rpx;
  background: #f8f9fa;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 动作列表 */
.exercise-list {
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.exercise-list.collapsed {
  max-height: 0;
}

.exercise-list.expanded {
  max-height: 2000rpx;
}

.exercise-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.exercise-item:last-child {
  border-bottom: none;
}

.exercise-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.exercise-image {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.exercise-info {
  flex: 1;
}

.exercise-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.exercise-sets {
  font-size: 26rpx;
  color: #666;
}

/* 组数详情 */
.set-records {
  margin-left: 80rpx;
}

.set-record {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.set-record:last-child {
  border-bottom: none;
}

.set-record.completed {
  opacity: 1;
}

.set-record.incomplete {
  opacity: 0.6;
}

.set-number {
  width: 120rpx;
  font-size: 26rpx;
  color: #666;
}

.set-weight,
.set-reps {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  text-align: center;
}

/* 评论区域 */
.comments-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.comments-section .section-title {
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 评论列表 */
.comments-list {
  padding: 0 24rpx;
}

.comment-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.comment-username {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

/* 评论操作 */
.comment-actions {
  display: flex;
  align-items: center;
}

.comment-action {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  padding: 8rpx 0;
}

.comment-action .action-text {
  font-size: 24rpx;
  color: #999;
}

.comment-action .action-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.comment-action.liked .action-text {
  color: #FF3B30;
}

/* 回复列表 */
.replies-list {
  margin-top: 16rpx;
  padding-left: 20rpx;
  border-left: 2rpx solid #f0f0f0;
}

.reply-item {
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-username {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-right: 8rpx;
}

.reply-to {
  font-size: 26rpx;
  color: #007AFF;
  margin-right: 8rpx;
}

.reply-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.reply-time {
  font-size: 22rpx;
  color: #999;
  margin-left: 16rpx;
}

/* 加载更多评论 */
.load-more-comments {
  text-align: center;
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.load-more-text {
  font-size: 28rpx;
  color: #007AFF;
}

/* 底部输入栏 */
.input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1rpx solid #e0e0e0;
  z-index: 1000;
  transition: bottom 0.3s ease;
}

.input-container.focused {
  /* 键盘弹起时的样式调整 */
}

/* 回复提示 */
.reply-tip {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e0e0e0;
}

.reply-text {
  font-size: 26rpx;
  color: #666;
}

.cancel-reply {
  font-size: 26rpx;
  color: #007AFF;
}

/* 输入框包装器 */
.input-wrapper {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
}

.comment-input {
  flex: 1;
  height: 72rpx;
  padding: 0 20rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  font-size: 28rpx;
  color: #333;
  margin-right: 16rpx;
}

.send-btn {
  width: 120rpx;
  height: 72rpx;
  background: #E5E5EA;
  color: #999;
  border: none;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.send-btn.active {
  background: #007AFF;
  color: #fff;
}

.send-btn::after {
  border: none;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .post-header,
  .post-text-content,
  .workout-stats,
  .exercise-item {
    padding: 20rpx;
  }
  
  .post-images {
    margin: 0 20rpx 20rpx;
  }
  
  .post-actions {
    padding: 16rpx 20rpx;
  }
} 