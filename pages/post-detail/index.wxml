<view class="post-detail-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 帖子内容 -->
  <view class="post-content-section" wx:if="{{post && !loading}}">
    <!-- 用户信息 -->
    <view class="post-header">
      <view class="user-info">
        <image class="avatar" src="{{post.user.avatar_url || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="user-details">
          <view class="username">{{post.user.nickname || post.user.username}}</view>
          <view class="post-time">{{post.created_at}}</view>
        </view>
      </view>
      <view class="follow-container" wx:if="{{!post.user.is_current_user}}">
        <button class="follow-btn {{post.user.is_following ? 'following' : ''}}" bindtap="onFollowUser">
          {{post.user.is_following ? '已关注' : '关注'}}
        </button>
      </view>
    </view>

    <!-- 帖子文本内容 -->
    <view class="post-text-content">
      <view class="post-title" wx:if="{{post.title}}">{{post.title}}</view>
      <view class="post-text">{{post.content}}</view>
    </view>

    <!-- 图片展示 -->
    <view class="post-images" wx:if="{{post.images && post.images.length > 0}}">
      <swiper class="image-swiper" indicator-dots="{{post.images.length > 1}}" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#fff">
        <block wx:for="{{post.images}}" wx:key="index">
          <swiper-item>
            <image class="post-image" src="{{item.url}}" mode="aspectFill" bindtap="onPreviewImage" data-index="{{index}}"></image>
          </swiper-item>
        </block>
      </swiper>
    </view>

    <!-- 交互按钮 -->
    <view class="post-actions">
      <view class="action-item {{post.is_liked_by_current_user ? 'liked' : ''}}" bindtap="onLikePost">
        <view class="action-icon">{{post.is_liked_by_current_user ? '❤️' : '🤍'}}</view>
        <view class="action-text">{{post.like_count || 0}}</view>
      </view>
      
      <view class="action-item">
        <view class="action-icon">💬</view>
        <view class="action-text">{{post.comment_count || 0}}</view>
      </view>
      
      <view class="action-item" bindtap="onSharePost">
        <view class="action-icon">↗️</view>
        <view class="action-text">分享</view>
      </view>
    </view>
  </view>

  <!-- 训练详情章节 -->
  <view class="workout-section" wx:if="{{post && post.related_workout_detail}}">
    <view class="section-header" bindtap="onToggleWorkoutDetail">
      <view class="section-title">训练记录</view>
      <view class="toggle-icon {{workoutExpanded ? 'expanded' : ''}}">▼</view>
    </view>
    
    <!-- 训练统计 -->
    <view class="workout-stats">
      <view class="stat-item">
        <text class="stat-value">{{post.related_workout_detail.actual_duration || 0}}</text>
        <text class="stat-label">分钟</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{post.related_workout_detail.total_volume || 0}}</text>
        <text class="stat-label">kg</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{post.related_workout_detail.workout_exercises.length || 0}}</text>
        <text class="stat-label">个动作</text>
      </view>
    </view>

    <!-- 动作列表 -->
    <view class="exercise-list {{workoutExpanded ? 'expanded' : 'collapsed'}}">
      <block wx:for="{{post.related_workout_detail.workout_exercises}}" wx:key="index">
        <view class="exercise-item">
          <view class="exercise-header">
            <image class="exercise-image" src="{{item.exercise_image || '/images/default-exercise.png'}}" mode="aspectFill"></image>
            <view class="exercise-info">
              <view class="exercise-name">{{item.exercise_name}}</view>
              <view class="exercise-sets">{{item.sets}}组</view>
            </view>
          </view>
          
          <!-- 组数详情 -->
          <view class="set-records" wx:if="{{workoutExpanded && item.set_records}}">
            <block wx:for="{{item.set_records}}" wx:key="set_number" wx:for-item="record">
              <view class="set-record {{record.completed ? 'completed' : 'incomplete'}}">
                <text class="set-number">第{{record.set_number}}组</text>
                <text class="set-weight">{{record.weight}}kg</text>
                <text class="set-reps">{{record.reps}}次</text>
              </view>
            </block>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 评论区域 -->
  <view class="comments-section" wx:if="{{post && !loading}}">
    <view class="section-title">评论 ({{post.comment_count || 0}})</view>
    
    <!-- 评论列表 -->
    <view class="comments-list">
      <block wx:for="{{comments}}" wx:key="id">
        <view class="comment-item">
          <image class="comment-avatar" src="{{item.user.avatar_url || '/images/default-avatar.png'}}" mode="aspectFill"></image>
          <view class="comment-content">
            <view class="comment-header">
              <text class="comment-username">{{item.user.nickname || item.user.username}}</text>
              <text class="comment-time">{{item.created_at}}</text>
            </view>
            <view class="comment-text">{{item.content}}</view>
            
            <!-- 回复按钮 -->
            <view class="comment-actions">
              <view class="comment-action" bindtap="onReplyComment" data-comment="{{item}}" data-user="{{item.user}}">
                <text class="action-text">回复</text>
              </view>
              <view class="comment-action {{item.is_liked_by_current_user ? 'liked' : ''}}" bindtap="onLikeComment" data-comment-id="{{item.id}}">
                <text class="action-icon">{{item.is_liked_by_current_user ? '❤️' : '🤍'}}</text>
                <text class="action-text">{{item.like_count || 0}}</text>
              </view>
            </view>

            <!-- 回复列表 -->
            <view class="replies-list" wx:if="{{item.replies && item.replies.length > 0}}">
              <block wx:for="{{item.replies}}" wx:key="id" wx:for-item="reply">
                <view class="reply-item">
                  <text class="reply-username">{{reply.user.nickname}}</text>
                  <text class="reply-to" wx:if="{{reply.reply_to_user}}">@{{reply.reply_to_user.nickname}}</text>
                  <text class="reply-content">{{reply.content}}</text>
                  <text class="reply-time">{{reply.created_at}}</text>
                </view>
              </block>
            </view>
          </view>
        </view>
      </block>
    </view>

    <!-- 加载更多评论 -->
    <view class="load-more-comments" wx:if="{{hasMoreComments}}" bindtap="loadMoreComments">
      <text class="load-more-text">{{loadingComments ? '加载中...' : '查看更多评论'}}</text>
    </view>
  </view>

  <!-- 底部输入栏 -->
  <view class="input-container {{inputFocused ? 'focused' : ''}}">
    <!-- 回复提示 -->
    <view class="reply-tip" wx:if="{{replyToComment}}">
      <text class="reply-text">回复 @{{replyToUser.nickname}}</text>
      <text class="cancel-reply" bindtap="onCancelReply">取消</text>
    </view>
    
    <view class="input-wrapper">
      <input 
        class="comment-input" 
        placeholder="{{replyToComment ? '写回复...' : '写评论...'}}"
        value="{{inputValue}}"
        bindinput="onInputChange"
        bindfocus="onInputFocus"
        bindblur="onInputBlur"
        confirm-type="send"
        bindconfirm="onSendComment"
      />
      <button class="send-btn {{inputValue.trim() ? 'active' : ''}}" bindtap="onSendComment">发送</button>
    </view>
  </view>
</view> 