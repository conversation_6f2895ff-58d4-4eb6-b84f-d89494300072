const request = require('./request');

/**
 * Training Plan API module
 * Provides functions to interact with training plan endpoints
 */

const BASE_URL = '/api/v1/training-plan';

/**
 * @typedef {Object} TrainingPlanGenRequest
 * @property {number} user_id - User ID
 * @property {number} duration_weeks - Duration in weeks
 * @property {number} days_per_week - Training days per week
 * @property {string} fitness_goal - User's fitness goal
 * @property {string[]} available_equipment - Available equipment list
 * @property {string[]} focus_body_parts - Target body parts
 * @property {number} time_per_workout - Time per workout in minutes
 * @property {string} [additional_notes] - Additional notes
 */

/**
 * @typedef {Object} DailyWorkoutGenRequest
 * @property {number} user_id - User ID
 * @property {number} available_time - Available time in minutes
 * @property {string[]} target_body_parts - Target body parts
 * @property {string[]} available_equipment - Available equipment
 * @property {string} recovery_level - Recovery level (low/moderate/high)
 * @property {string} [additional_notes] - Additional notes
 */

/**
 * @typedef {Object} TrainingPlanBase
 * @property {number} id - Plan ID
 * @property {number} user_id - User ID
 * @property {string} name - Plan name
 * @property {'active'|'completed'|'paused'} status - Plan status
 * @property {boolean} is_active - Whether the plan is active
 * @property {boolean} is_template - Whether the plan is a template
 * @property {number} privacy_setting - Privacy setting (0: Public, 1: Private)
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 */

/**
 * @typedef {Object} WorkoutExercise
 * @property {number} id - Exercise ID
 * @property {number} workout_id - Workout ID
 * @property {number} exercise_id - Exercise definition ID
 * @property {string} exercise_name - Exercise name
 * @property {number} sets - Number of sets
 * @property {number} reps - Number of reps
 * @property {number} [weight] - Weight in kg
 * @property {number} rest_time - Rest time in seconds
 * @property {string} [notes] - Exercise notes
 * @property {number} order - Exercise order in workout
 */

/**
 * @typedef {Object} Workout
 * @property {number} id - Workout ID
 * @property {number} training_plan_id - Training plan ID
 * @property {number} day - Day number
 * @property {string} [notes] - Workout notes
 * @property {WorkoutExercise[]} exercises - List of exercises
 */

/**
 * @typedef {Object} TrainingPlanDetail
 * @property {Workout[]} workouts - List of workouts
 * @property {number} duration_weeks - Duration in weeks
 * @property {number} days_per_week - Training days per week
 * @property {string} fitness_goal - Fitness goal
 * @property {string[]} available_equipment - Available equipment
 * @property {string[]} focus_body_parts - Focus body parts
 * @property {number} time_per_workout - Time per workout
 * @property {string} [additional_notes] - Additional notes
 */

/**
 * Generate a new training plan for a user
 * @param {TrainingPlanGenRequest} params - Training plan parameters
 * @returns {Promise<TrainingPlanDetail>} Generated training plan
 */
function generatePlan(params) {
  return request.post(`${BASE_URL}/generate`, params);
}

/**
 * Generate a daily workout plan
 * @param {DailyWorkoutGenRequest} params - Daily workout parameters
 * @returns {Promise<Workout>} Generated daily workout
 */
function generateDailyWorkout(params) {
  return request.post(`${BASE_URL}/daily`, params);
}

/**
 * Get user's training plans
 * @param {Object} [params] - Query parameters
 * @param {number} [params.skip=0] - Number of records to skip
 * @param {number} [params.limit=100] - Number of records to return
 * @param {boolean} [params.active_only=true] - Only return active plans
 * @returns {Promise<TrainingPlanBase[]>} List of training plans
 */
function getUserPlans(params = {}) {
  const defaultParams = {
    skip: 0,
    limit: 100,
    active_only: true,
    ...params
  };
  return request.get(BASE_URL, { data: defaultParams });
}

/**
 * Get detailed information about a specific training plan
 * @param {string|number} planId - Training plan ID
 * @returns {Promise<TrainingPlanDetail>} Training plan details
 */
function getPlanDetail(planId) {
  console.log('【getPlanDetail】调用API获取训练计划详情, planId:', planId);
  return request.get(`${BASE_URL}/${planId}`)
    .then(response => {
      console.log('【getPlanDetail】API返回数据:', response);
      return response;
    })
    .catch(error => {
      console.error('【getPlanDetail】API请求失败:', error);
      throw error;
    });
}

/**
 * @typedef {Object} StatusUpdate
 * @property {'active'|'completed'|'paused'} status - New status
 * @property {boolean} [is_active] - Active status flag
 */

/**
 * Update training plan status
 * @param {string|number} planId - Training plan ID
 * @param {StatusUpdate} params - Status update parameters
 * @returns {Promise<TrainingPlanBase>} Updated plan status
 */
function updatePlanStatus(planId, params) {
  return request.post(`${BASE_URL}/${planId}/status`, params);
}

/**
 * @typedef {Object} TemplateCreate
 * @property {string} [name] - Template name
 */

/**
 * Create a template from an existing training plan
 * @param {string|number} planId - Training plan ID
 * @param {TemplateCreate} [params] - Template parameters
 * @returns {Promise<TrainingPlanBase>} Created template
 */
function createTemplate(planId, params = {}) {
  return request.post(`${BASE_URL}/${planId}/template`, params);
}

/**
 * Create a new training plan from a template
 * @param {string|number} templateId - Template ID
 * @param {TemplateCreate} [params] - Plan parameters
 * @returns {Promise<TrainingPlanDetail>} Created training plan
 */
function createPlanFromTemplate(templateId, params = {}) {
  return request.post(`${BASE_URL}/templates/${templateId}/create`, params);
}

/**
 * Delete a training plan
 * @param {string|number} planId - Training plan ID
 * @returns {Promise<void>} Deletion result
 */
function deletePlan(planId) {
  return request.delete(`${BASE_URL}/${planId}`);
}

/**
 * @typedef {Object} SetRecord
 * @property {number} id - Record ID
 * @property {number} training_record_id - Training record ID
 * @property {number} workout_exercise_id - Workout exercise ID
 * @property {number} set_number - Set number
 * @property {number} reps - Reps completed
 * @property {number} weight - Weight used
 * @property {boolean} completed - Whether the set was completed
 * @property {string} [notes] - Set notes
 */

/**
 * @typedef {Object} TrainingRecord
 * @property {number} id - Record ID
 * @property {number} user_id - User ID
 * @property {number} workout_id - Workout ID
 * @property {string} date - Training date
 * @property {number} duration - Duration in minutes
 * @property {string} [notes] - Training notes
 * @property {SetRecord[]} set_records - Set records
 */

/**
 * @typedef {Object} TrainingPlanUpdate
 * @property {Partial<TrainingPlanBase>} [plan_data] - Plan data updates
 * @property {Array<Partial<Workout>>} [workout_data] - Workout data updates
 * @property {Array<Partial<WorkoutExercise>>} [workout_exercise_data] - Exercise data updates
 * @property {Array<Partial<TrainingRecord>>} [training_record_data] - Training record updates
 * @property {Array<Partial<SetRecord>>} [set_record_data] - Set record updates
 */

/**
 * Update various aspects of a training plan
 * @param {string|number} planId - Training plan ID
 * @param {TrainingPlanUpdate} params - Update parameters
 * @returns {Promise<TrainingPlanDetail>} Updated training plan
 */
function updatePlan(planId, params) {
  // 在控制台打印准备发送的数据
  console.log('【updatePlan】准备发送的数据:', {
    url: `${BASE_URL}/${planId}`,
    method: 'PUT',
    data: params
  });

  // 使用PUT方法发送请求
  return request.put(`${BASE_URL}/${planId}`, params);
}

/**
 * Get workouts by date
 * @param {string} date - Date in YYYY-MM-DD format
 * @returns {Promise<Array>} List of workouts scheduled for the date
 */
function getWorkoutsByDate(date) {
  // 直接将日期作为查询参数传递，而不是嵌套在 data 对象中
  return request.get(`${BASE_URL}/workouts/by-date?date=${date}`);
}

/**
 * Update workout status
 * @param {string|number} workoutId - Workout ID
 * @param {Object} params - Status update parameters
 * @param {string} params.status - New status (not_started, in_progress, paused, completed)
 * @returns {Promise<Object>} Updated workout info
 */
function updateWorkoutStatus(workoutId, params) {
  return request.post(`${BASE_URL}/workouts/${workoutId}/status`, params);
}

/**
 * Create a new workout for a training plan
 * @param {string|number} planId - Training plan ID
 * @param {Object} params - Workout parameters
 * @param {string} params.scheduled_date - Scheduled date (YYYY-MM-DD)
 * @param {string} [params.status="not_started"] - Workout status
 * @returns {Promise<Object>} Created workout info
 */
function createWorkout(planId, params) {
  // 构建请求数据
  const requestData = {
    workout_data: [{
      scheduled_date: params.scheduled_date,
      status: params.status || 'not_started'
    }]
  };

  // 在控制台打印准备发送的数据
  console.log('【createWorkout】准备发送的数据:', {
    planId: planId,
    requestData: requestData
  });

  // 使用updatePlan函数创建训练记录
  return updatePlan(planId, requestData);
}

/**
 * Get all workouts for a specific training plan
 * @param {string|number} planId - Training plan ID
 * @returns {Promise<Array>} List of workouts for the plan
 */
function getPlanWorkouts(planId) {
  console.log('【getPlanWorkouts】调用API获取训练计划的所有训练, planId:', planId);
  return request.get(`${BASE_URL}/${planId}/workouts`)
    .then(response => {
      console.log('【getPlanWorkouts】API返回数据:', response);
      return response;
    })
    .catch(error => {
      console.error('【getPlanWorkouts】API请求失败:', error);
      throw error;
    });
}

module.exports = {
  generatePlan,
  generateDailyWorkout,
  getUserPlans,
  getPlanDetail,
  updatePlanStatus,
  createTemplate,
  createPlanFromTemplate,
  deletePlan,
  updatePlan,
  getWorkoutsByDate,
  updateWorkoutStatus,
  createWorkout,
  getPlanWorkouts
};